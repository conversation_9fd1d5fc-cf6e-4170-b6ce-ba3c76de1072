import { Server } from "impress";
import { HealthcheckService } from "./healthcheck";
import bot from "../bot";
import { log } from "../utils/logger";

export class ImpressHttpServer {
  private server: Server | null = null;
  private readonly port: number;
  private isReady = false;

  constructor(port: number = 8080) {
    this.port = port;
  }

  setReady(ready: boolean): void {
    this.isReady = ready;
    log.info(`Server readiness: ${ready ? "READY" : "NOT READY"}`, {
      operation: "http_server",
      ready,
    });
  }

  private setupRoutes(server: Server): void {
    // Health check endpoint
    server.get("/healthcheck", async (req, res) => {
      try {
        const lastHealthcheck = await HealthcheckService.getLastHealthcheck();
        const isHealthy = await HealthcheckService.isHealthy();

        const response = {
          status: isHealthy ? "healthy" : "unhealthy",
          lastHealthcheck,
          timestamp: new Date().toISOString(),
          service: "marketplace-bot",
        };

        res.status(isHealthy ? 200 : 503).json(response);
      } catch (error) {
        log.error("Error in healthcheck endpoint", error, {
          operation: "healthcheck",
        });

        const errorResponse = {
          status: "error",
          message: "Failed to check health status",
          timestamp: new Date().toISOString(),
          service: "marketplace-bot",
        };

        res.status(500).json(errorResponse);
      }
    });

    // Readiness endpoint
    server.get("/readiness", (req, res) => {
      const response = {
        status: this.isReady ? "ready" : "not ready",
        timestamp: new Date().toISOString(),
        service: "marketplace-bot",
      };

      res.status(this.isReady ? 200 : 503).json(response);
    });

    // Webhook endpoint
    server.post("/webhook", async (req, res) => {
      try {
        const update = req.body;
        
        log.webhookLog("Received webhook update", {
          operation: "webhook_processing",
          updateId: update.update_id,
        });

        // Process the update with Telegraf
        await bot.handleUpdate(update);

        res.json({ ok: true });
      } catch (error) {
        log.error("Error processing webhook update", error, {
          operation: "webhook_processing",
        });
        res.status(500).json({ ok: false, error: "Failed to process update" });
      }
    });

    // Root endpoint
    server.get("/", (req, res) => {
      const response = {
        service: "marketplace-bot",
        status: "running",
        ready: this.isReady,
        timestamp: new Date().toISOString(),
        endpoints: ["/healthcheck", "/readiness", "/webhook"],
      };

      res.json(response);
    });

    // 404 handler for unknown routes
    server.use("*", (req, res) => {
      const notFoundResponse = {
        error: "Not Found",
        message: `Route ${req.url} not found`,
        timestamp: new Date().toISOString(),
      };

      res.status(404).json(notFoundResponse);
    });

    // Global error handler
    server.use((error: Error, req: any, res: any, next: any) => {
      log.error("Request handling error", error, {
        operation: "http_request",
        url: req.url,
        method: req.method,
      });

      if (!res.headersSent) {
        res.status(500).json({ error: "Internal Server Error" });
      }
    });
  }

  start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = new Server({
          port: this.port,
          cors: {
            origin: "*",
            methods: ["GET", "POST"],
            allowedHeaders: ["Content-Type"],
          },
          json: { limit: "10mb" },
          urlencoded: { extended: true },
        });

        this.setupRoutes(this.server);

        this.server.listen(() => {
          log.info(`HTTP server running on port ${this.port}`, {
            operation: "http_server_start",
            port: this.port,
            healthcheckUrl: `http://localhost:${this.port}/healthcheck`,
          });
          resolve();
        });

        this.server.on("error", (error: Error) => {
          log.error("HTTP server error", error, {
            operation: "http_server_start",
            port: this.port,
          });
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        log.info("Stopping HTTP server", {
          operation: "http_server_stop",
        });

        this.server.close(() => {
          log.info("HTTP server stopped", {
            operation: "http_server_stop",
            status: "completed",
          });
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

const PORT = parseInt(process.env.PORT ?? "8080");
export const impressHttpServer = new ImpressHttpServer(PORT);
