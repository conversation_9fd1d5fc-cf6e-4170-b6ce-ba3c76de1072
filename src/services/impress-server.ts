import { createServer, IncomingMessage, ServerResponse } from "http";
import { HealthcheckService } from "./healthcheck";
import bot from "../bot";
import { log } from "../utils/logger";

// Simplified HTTP server with better organization and less code
export class SimplifiedHttpServer {
  private server: ReturnType<typeof createServer> | null = null;
  private readonly port: number;
  private readonly connections = new Set<any>();
  private isReady = false;

  constructor(port: number = 8080) {
    this.port = port;
  }

  setReady(ready: boolean): void {
    this.isReady = ready;
    log.info(`Server readiness: ${ready ? "READY" : "NOT READY"}`, {
      operation: "http_server",
      ready,
    });
  }

  // Simplified response helper
  private sendJson(res: ServerResponse, statusCode: number, data: any): void {
    res.writeHead(statusCode, {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
    });
    res.end(JSON.stringify(data, null, 2));
  }

  // Route handlers organized as methods
  private async handleHealthcheck(res: ServerResponse): Promise<void> {
    try {
      const lastHealthcheck = await HealthcheckService.getLastHealthcheck();
      const isHealthy = await HealthcheckService.isHealthy();

      const response = {
        status: isHealthy ? "healthy" : "unhealthy",
        lastHealthcheck,
        timestamp: new Date().toISOString(),
        service: "marketplace-bot",
      };

      this.sendJson(res, isHealthy ? 200 : 503, response);
    } catch (error) {
      log.error("Error in healthcheck endpoint", error, {
        operation: "healthcheck",
      });

      const errorResponse = {
        status: "error",
        message: "Failed to check health status",
        timestamp: new Date().toISOString(),
        service: "marketplace-bot",
      };

      this.sendJson(res, 500, errorResponse);
    }
  }

  private handleReadiness(res: ServerResponse): void {
    const response = {
      status: this.isReady ? "ready" : "not ready",
      timestamp: new Date().toISOString(),
      service: "marketplace-bot",
    };

    this.sendJson(res, this.isReady ? 200 : 503, response);
  }

  private async handleWebhook(
    req: IncomingMessage,
    res: ServerResponse
  ): Promise<void> {
    return new Promise((resolve) => {
      let body = "";

      req.on("data", (chunk) => {
        body += chunk.toString();
      });

      req.on("end", async () => {
        try {
          const update = JSON.parse(body);

          log.webhookLog("Received webhook update", {
            operation: "webhook_processing",
            updateId: update.update_id,
          });

          await bot.handleUpdate(update);
          this.sendJson(res, 200, { ok: true });
        } catch (error) {
          log.error("Error processing webhook update", error, {
            operation: "webhook_processing",
          });
          this.sendJson(res, 500, {
            ok: false,
            error: "Failed to process update",
          });
        }
        resolve();
      });

      req.on("error", (error) => {
        log.error("Error reading webhook request", error, {
          operation: "webhook_processing",
        });
        this.sendJson(res, 400, { ok: false, error: "Bad request" });
        resolve();
      });
    });
  }

  private handleRoot(res: ServerResponse): void {
    const response = {
      service: "marketplace-bot",
      status: "running",
      ready: this.isReady,
      timestamp: new Date().toISOString(),
      endpoints: ["/healthcheck", "/readiness", "/webhook"],
    };

    this.sendJson(res, 200, response);
  }

  private handle404(req: IncomingMessage, res: ServerResponse): void {
    const notFoundResponse = {
      error: "Not Found",
      message: `Route ${req.url} not found`,
      timestamp: new Date().toISOString(),
    };

    this.sendJson(res, 404, notFoundResponse);
  }

  // Simplified request router
  private async handleRequest(
    req: IncomingMessage,
    res: ServerResponse
  ): Promise<void> {
    const { url, method } = req;

    log.info(`${method} ${url}`, {
      operation: "http_request",
      method,
      url,
    });

    // Route mapping with simplified logic
    const routes = {
      "GET /healthcheck": () => this.handleHealthcheck(res),
      "GET /readiness": () => this.handleReadiness(res),
      "POST /webhook": () => this.handleWebhook(req, res),
      "GET /": () => this.handleRoot(res),
    };

    const routeKey = `${method} ${url}`;
    const handler = routes[routeKey as keyof typeof routes];

    if (handler) {
      await handler();
    } else {
      this.handle404(req, res);
    }
  }

  start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = createServer((req, res) => {
          this.handleRequest(req, res).catch((error) => {
            log.error("Request handling error", error, {
              operation: "http_request",
            });
            if (!res.headersSent) {
              this.sendJson(res, 500, { error: "Internal Server Error" });
            }
          });
        });

        // Track connections for graceful shutdown
        this.server.on("connection", (socket) => {
          this.connections.add(socket);
          socket.on("close", () => {
            this.connections.delete(socket);
          });
        });

        this.server.listen(this.port, () => {
          log.info(`HTTP server running on port ${this.port}`, {
            operation: "http_server_start",
            port: this.port,
            healthcheckUrl: `http://localhost:${this.port}/healthcheck`,
          });
          resolve();
        });

        this.server.on("error", (error) => {
          log.error("HTTP server error", error, {
            operation: "http_server_start",
            port: this.port,
          });
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        log.info("Stopping HTTP server", {
          operation: "http_server_stop",
        });

        // Close all active connections
        for (const socket of this.connections) {
          socket.destroy();
        }
        this.connections.clear();

        this.server.close(() => {
          log.info("HTTP server stopped", {
            operation: "http_server_stop",
            status: "completed",
          });
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

const PORT = parseInt(process.env.PORT ?? "8080");
export const simplifiedHttpServer = new SimplifiedHttpServer(PORT);
