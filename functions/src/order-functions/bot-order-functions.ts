import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { addFunds, spendLockedFunds } from "../services/balance-service";
import { verifyBotToken } from "../services/bot-auth-service";
import { applyPurchaseFeeWithReferralFromOrder } from "../services/fee-service";
import { OrderEntity } from "../types";
import { safeMultiply, safeSubtract } from "../utils";
import { CORS_CONFIG } from "../config";
import { log } from "../utils/logger";

export const getOrderByIdByBot = onCall<{
  orderId: string;
  botToken: string;
}>({ cors: CORS_CONFIG }, async (request) => {
  const { orderId, botToken } = request.data;

  if (!orderId) {
    throw new HttpsError("invalid-argument", "Order ID is required.");
  }

  if (!botToken) {
    throw new HttpsError("invalid-argument", "Bot token is required.");
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError("permission-denied", "Invalid bot token.");
  }

  try {
    const db = admin.firestore();
    const orderDoc = await db.collection("orders").doc(orderId).get();

    if (!orderDoc.exists) {
      return {
        success: false,
        order: null,
        message: "Order not found.",
      };
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    return {
      success: true,
      order,
      message: "Order retrieved successfully.",
    };
  } catch (error) {
    console.error("Error getting order by ID:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while getting order."
    );
  }
});

export const getUserOrdersByBot = onCall<{
  userId?: string;
  tgId?: string;
  botToken: string;
}>({ cors: CORS_CONFIG }, async (request) => {
  const { userId, tgId, botToken } = request.data;

  if (!botToken) {
    throw new HttpsError("invalid-argument", "Bot token is required.");
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError("permission-denied", "Invalid bot token.");
  }

  try {
    const db = admin.firestore();
    let targetUserId = userId;

    if (!targetUserId && tgId) {
      const usersQuery = await db
        .collection("users")
        .where("tg_id", "==", tgId)
        .limit(1)
        .get();

      if (usersQuery.empty) {
        return {
          success: false,
          orders: [],
          sellOrders: [],
          buyOrders: [],
          count: 0,
          sellOrdersCount: 0,
          buyOrdersCount: 0,
          userId: "",
          message: "User not found with the provided Telegram ID.",
        };
      }

      targetUserId = usersQuery.docs[0].id;
    }

    if (!targetUserId) {
      throw new HttpsError(
        "invalid-argument",
        "Either userId or tgId is required."
      );
    }

    const sellOrdersQuery = await db
      .collection("orders")
      .where("sellerId", "==", targetUserId)
      .where("status", "==", "paid")
      .get();

    const buyOrdersQuery = await db
      .collection("orders")
      .where("buyerId", "==", targetUserId)
      .where("status", "==", "gift_sent_to_relayer")
      .get();

    const sellOrders: OrderEntity[] = [];
    const buyOrders: OrderEntity[] = [];

    sellOrdersQuery.forEach((doc) => {
      sellOrders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    buyOrdersQuery.forEach((doc) => {
      buyOrders.push({ id: doc.id, ...doc.data() } as OrderEntity);
    });

    const sortByDate = (a: OrderEntity, b: OrderEntity) => {
      const aTime = a.createdAt?.toMillis() || 0;
      const bTime = b.createdAt?.toMillis() || 0;
      return bTime - aTime;
    };

    sellOrders.sort(sortByDate);
    buyOrders.sort(sortByDate);

    const allOrders = [...sellOrders, ...buyOrders];
    allOrders.sort(sortByDate);

    return {
      success: true,
      orders: allOrders,
      sellOrders,
      buyOrders,
      count: allOrders.length,
      sellOrdersCount: sellOrders.length,
      buyOrdersCount: buyOrders.length,
      userId: targetUserId,
    };
  } catch (error) {
    log.error("Error getting user orders", error, {
      userId: request.data.userId,
      operation: "get_user_orders_by_bot",
    });
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while getting user orders."
    );
  }
});

export const sendGiftToRelayerByBot = onCall<{
  orderId: string;
  botToken: string;
  owned_gift_id: string;
}>({ cors: CORS_CONFIG }, async (request) => {
  const { orderId, botToken, owned_gift_id } = request.data;

  if (!owned_gift_id) {
    throw new HttpsError("invalid-argument", "Owned gift ID is required.");
  }

  if (!orderId) {
    throw new HttpsError("invalid-argument", "Order ID is required.");
  }

  if (!botToken) {
    throw new HttpsError("invalid-argument", "Bot token is required.");
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError("permission-denied", "Invalid bot token.");
  }

  try {
    const db = admin.firestore();

    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    if (order.status !== "paid") {
      throw new HttpsError(
        "failed-precondition",
        "Order must be in 'paid' status to send gift to relayer."
      );
    }

    if (!order.buyerId) {
      throw new HttpsError("failed-precondition", "Order has no buyer.");
    }

    await db.collection("orders").doc(orderId).update({
      status: "gift_sent_to_relayer",
      owned_gift_id,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: "Gift sent to relayer successfully. Buyer will be notified.",
      order: {
        id: order.id,
        number: order.number,
        status: "gift_sent_to_relayer",
      },
    };
  } catch (error) {
    log.error("Error sending gift to relayer", error, {
      orderId,
      operation: "send_gift_to_relayer_by_bot",
    });
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while sending gift to relayer."
    );
  }
});

export const completePurchaseByBot = onCall<{
  orderId: string;
  botToken: string;
}>({ cors: CORS_CONFIG }, async (request) => {
  const { orderId, botToken } = request.data;

  if (!orderId) {
    throw new HttpsError("invalid-argument", "Order ID is required.");
  }

  if (!botToken) {
    throw new HttpsError("invalid-argument", "Bot token is required.");
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError("permission-denied", "Invalid bot token.");
  }

  try {
    const db = admin.firestore();

    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    if (order.status !== "gift_sent_to_relayer") {
      throw new HttpsError(
        "failed-precondition",
        "Order must be in 'gift_sent_to_relayer' status to complete purchase."
      );
    }

    if (!order.buyerId || !order.sellerId) {
      throw new HttpsError(
        "failed-precondition",
        "Order must have both buyer and seller."
      );
    }

    const buyerDoc = await db.collection("users").doc(order.buyerId).get();
    const buyerData = buyerDoc.data();
    const referralId = buyerData?.referrer_id;

    // Use fees from order object instead of app_config
    const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage ?? 0;
    const sellerLockPercentageBPS = order.fees?.seller_locked_percentage ?? 0;
    const buyerLockPercentage = buyerLockPercentageBPS / 10000; // Convert BPS to decimal
    const sellerLockPercentage = sellerLockPercentageBPS / 10000; // Convert BPS to decimal

    const buyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);
    const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

    // Use purchase fee from order object
    const purchaseFeeBPS = order.fees?.purchase_fee ?? 0;
    const referrerFeeBPS = order.fees?.referrer_fee ?? 0;

    const feeResult = await applyPurchaseFeeWithReferralFromOrder({
      buyerId: order.buyerId,
      amount: order.price,
      referralId,
      purchaseFeeBPS,
      referrerFeeBPS,
    });

    const netAmountToSeller = safeSubtract(order.price, feeResult.totalFee);

    // Add reseller earnings for seller if any
    const resellerEarnings = order.reseller_earnings_for_seller ?? 0;
    const totalAmountToSeller = netAmountToSeller + resellerEarnings;

    await Promise.all([
      spendLockedFunds(order.buyerId, buyerLockedAmount),
      spendLockedFunds(order.sellerId, sellerLockedAmount),
    ]);

    await addFunds(order.sellerId, totalAmountToSeller);

    await db.collection("orders").doc(orderId).update({
      status: "fulfilled",
      owned_gift_id: null,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: "Purchase completed successfully.",
      netAmountToSeller,
      resellerEarnings,
      totalAmountToSeller,
      feeAmount: feeResult.totalFee,
      order: {
        id: order.id,
        number: order.number,
        status: "fulfilled",
      },
    };
  } catch (error) {
    log.error("Error completing purchase", error, {
      orderId,
      operation: "complete_purchase_by_bot",
    });
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while completing purchase."
    );
  }
});
