import { HttpsError, onCall } from "firebase-functions/v2/https";
import {
  getUserData,
  requireAuthentication,
  requireTonWallet,
  validatePositiveAmount,
} from "./services/auth-middleware";
import {
  hasAvailableBalance,
  updateUserBalance,
} from "./services/balance-service";
import { applyWithdrawFee, getAppConfig } from "./services/fee-service";
import { getTonWalletService } from "./services/ton-wallet-service";
import { safeSubtract } from "./utils";
import { CORS_CONFIG } from "./config";

export const withdrawFunds = onCall<{
  amount: number;
}>({ cors: CORS_CONFIG }, async (request) => {
  const authRequest = requireAuthentication(request);
  const { amount } = request.data;

  validatePositiveAmount({ amount });

  try {
    const userId = authRequest.auth.uid;

    const user = await getUserData(userId);
    requireTonWallet(user);

    const appConfig = await getAppConfig();
    if (appConfig) {
      const minWithdrawal = appConfig.min_withdrawal_amount || 0;
      const maxWithdrawal =
        appConfig.max_withdrawal_amount || Number.MAX_SAFE_INTEGER;

      if (amount < minWithdrawal) {
        throw new HttpsError(
          "failed-precondition",
          `Withdrawal amount must be at least ${minWithdrawal} TON.`
        );
      }

      if (amount > maxWithdrawal) {
        throw new HttpsError(
          "failed-precondition",
          `Withdrawal amount cannot exceed ${maxWithdrawal} TON.`
        );
      }
    }

    const hasBalance = await hasAvailableBalance(userId, amount);
    if (!hasBalance) {
      throw new HttpsError(
        "failed-precondition",
        "Insufficient available balance for withdrawal."
      );
    }

    // Apply withdrawal fee and get net amount
    const feeAmount = await applyWithdrawFee(userId, amount);
    const netAmountToUser = safeSubtract(amount, feeAmount);

    if (netAmountToUser <= 0) {
      throw new HttpsError(
        "failed-precondition",
        "Amount too small after fees."
      );
    }

    // Deduct the full amount from user's balance (including fee)
    await updateUserBalance({
      userId,
      sumChange: -amount,
      lockedChange: 0,
    });

    // Use TON wallet service to send withdrawal
    const tonWalletService = getTonWalletService();
    const transferResult = await tonWalletService.sendWithdrawal(
      netAmountToUser,
      user.ton_wallet_address!
    );

    console.log(
      `Withdrawal processed: ${netAmountToUser} TON sent to ${user.ton_wallet_address} (${feeAmount} TON fee applied)`
    );

    return {
      success: transferResult.success,
      message: `Withdrawal successful. ${netAmountToUser} TON sent to your wallet (${feeAmount} TON fee applied)`,
      netAmount: netAmountToUser,
      feeAmount,
      transactionHash: transferResult.transactionHash,
    };
  } catch (error) {
    console.error("Error processing withdrawal:", JSON.stringify(error));
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while processing withdrawal."
    );
  }
});
