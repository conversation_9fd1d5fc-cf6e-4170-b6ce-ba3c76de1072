import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import {
  getTelegramBotToken,
  isDevelopment,
  CORS_CONFIG,
  getConfig,
} from "./config";
import { UserEntity } from "./types";
import * as CryptoJS from "crypto-js";

export interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  photo_url?: string;
}

export interface ValidationResult {
  isValid: boolean;
  user?: TelegramUser;
  error?: string;
}

export function validateTelegramWebAppData(initData: string, botToken: string) {
  try {
    // Parse the init data
    const urlParams = new URLSearchParams(initData);
    const hash = urlParams.get("hash");

    if (!hash) {
      return { isValid: false, error: "Hash is missing" };
    }

    // Remove hash from params for validation
    urlParams.delete("hash");

    // Sort parameters alphabetically and create data check string
    const dataCheckArray: string[] = [];
    for (const [key, value] of Array.from(urlParams.entries()).sort()) {
      dataCheckArray.push(`${key}=${value}`);
    }
    const dataCheckString = dataCheckArray.join("\n");

    // Create secret key using bot token
    const secretKey = CryptoJS.HmacSHA256(botToken, "WebAppData");

    // Calculate expected hash
    const expectedHash = CryptoJS.HmacSHA256(
      dataCheckString,
      secretKey
    ).toString();

    // Verify hash
    if (hash !== expectedHash && !isDevelopment()) {
      return { isValid: false, error: "Invalid hash" };
    }

    // Check auth_date (should not be older than 24 hours)
    const authDate = urlParams.get("auth_date");
    if (!authDate) {
      return { isValid: false, error: "Auth date is missing" };
    }

    const authTimestamp = parseInt(authDate, 10);
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const maxAge = 24 * 60 * 60; // 24 hours in seconds

    if (currentTimestamp - authTimestamp > maxAge) {
      return { isValid: false, error: "Auth data is too old" };
    }

    // Parse user data
    const userParam = urlParams.get("user");
    if (!userParam) {
      return { isValid: false, error: "User data is missing" };
    }

    const user: TelegramUser = JSON.parse(userParam);

    return { isValid: true, user };
  } catch (error) {
    return {
      isValid: false,
      error: `Validation error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

async function validateTelegramData(
  initData: string,
  botToken: string,
  isDevMode: boolean
) {
  let telegramUser: TelegramUser;
  let telegramId: string;

  if (isDevMode && initData.includes("mock_hash_for_development")) {
    console.log("[validateTelegramData] Using development mode with mock data");

    const urlParams = new URLSearchParams(initData);
    const userParam = urlParams.get("user");

    if (!userParam) {
      throw new HttpsError(
        "invalid-argument",
        "User data is missing in mock initData"
      );
    }

    telegramUser = JSON.parse(userParam);
    telegramId = telegramUser.id.toString();

    console.log("[validateTelegramData] Mock user:", telegramUser);
  } else {
    console.log("[validateTelegramData] Validating Telegram data...");
    const validation = validateTelegramWebAppData(initData, botToken);

    console.log("[validateTelegramData] Validation result:", {
      isValid: validation.isValid,
      error: validation.error,
      hasUser: !!validation.user,
    });

    if (!validation.isValid || !validation.user) {
      throw new HttpsError(
        "permission-denied",
        validation.error ?? "Invalid Telegram data"
      );
    }

    telegramUser = validation.user;
    telegramId = telegramUser.id.toString();
  }

  return { telegramUser, telegramId };
}

async function createFirebaseAuthUser(
  userId: string,
  userRecord: UserEntity,
  telegramUser: TelegramUser,
  telegramId: string
): Promise<{ customToken: string }> {
  try {
    // First, try to get existing Firebase Auth user
    try {
      await admin.auth().getUser(userId);
      console.log("[createFirebaseAuthUser] Found existing Firebase Auth user");
    } catch (getUserError: any) {
      if (getUserError?.errorInfo?.code === "auth/user-not-found") {
        // Create Firebase Auth user if it doesn't exist
        console.log("[createFirebaseAuthUser] Creating new Firebase Auth user");
        await admin.auth().createUser({
          uid: userId,
          displayName: userRecord.displayName ?? undefined,
          photoURL: userRecord.photoURL ?? undefined,
        });
        console.log(
          "[createFirebaseAuthUser] Firebase Auth user created successfully"
        );
      } else {
        throw getUserError;
      }
    }

    // Prepare custom claims for the token
    const customClaims = {
      tg_id: telegramId,
      provider: "telegram",
      telegram_user: {
        id: telegramUser.id,
        first_name: telegramUser.first_name,
        last_name: telegramUser.last_name,
        username: telegramUser.username,
      },
    };

    // Set custom claims on the Firebase Auth user
    await admin.auth().setCustomUserClaims(userId, customClaims);
    console.log("[createFirebaseAuthUser] Custom claims set successfully");

    console.log("APP CONFIG IS", JSON.stringify(getConfig()));

    // Create custom token using Firebase Admin SDK
    const customToken = await admin
      .auth()
      .createCustomToken(userId, customClaims);
    console.log("[createFirebaseAuthUser] Custom token created successfully");

    return { customToken };
  } catch (authError: any) {
    console.error("[createFirebaseAuthUser] Error:", authError);

    // Handle specific IAM permission errors
    if (
      authError?.message?.includes("iam.serviceAccounts.signBlob") ||
      authError?.errorInfo?.code === "auth/insufficient-permission"
    ) {
      throw new HttpsError(
        "failed-precondition",
        "Firebase service account lacks required IAM permissions for custom token creation. Please grant 'Service Account Token Creator' role."
      );
    }

    // Re-throw other auth errors
    throw new HttpsError(
      "internal",
      `Firebase Auth error: ${authError.message || "Unknown error"}`
    );
  }
}

export const signInWithTelegram = onCall<{
  initData: string;
  useLocalBotToken?: boolean;
}>({ cors: CORS_CONFIG }, async (request) => {
  console.log("[signInWithTelegram] Starting authentication");

  try {
    const isDevMode = isDevelopment();
    const { initData, useLocalBotToken } = request.data;

    if (!initData) {
      throw new HttpsError("invalid-argument", "initData is required");
    }

    console.log("[signInWithTelegram] Development mode:", isDevMode);

    // Get bot token from Firebase config
    const botToken = getTelegramBotToken(useLocalBotToken);
    if (!botToken) {
      throw new HttpsError(
        "failed-precondition",
        "Telegram bot token not configured"
      );
    }

    const db = admin.firestore();

    // Validate and extract Telegram user data
    const { telegramUser, telegramId } = await validateTelegramData(
      initData,
      botToken,
      isDevMode
    );

    console.log("[signInWithTelegram] Final user data:", {
      telegramId,
      firstName: telegramUser.first_name,
      username: telegramUser.username,
    });

    // Check if user already exists
    const existingUserQuery = await db
      .collection("users")
      .where("tg_id", "==", telegramId)
      .limit(1)
      .get();

    let userId: string;
    let userRecord: UserEntity;

    if (!existingUserQuery.empty) {
      // User exists, get their data
      const existingUserDoc = existingUserQuery.docs[0];
      userId = existingUserDoc.id;
      userRecord = existingUserDoc.data() as UserEntity;
    } else {
      // Create new user
      const newUserRef = db.collection("users").doc();
      userId = newUserRef.id;

      userRecord = {
        id: userId,
        email: null,
        displayName:
          telegramUser.first_name +
          (telegramUser.last_name ? ` ${telegramUser.last_name}` : ""),
        photoURL: telegramUser.photo_url ?? null,
        role: "user",
        tg_id: telegramId,
        telegram_handle: telegramUser.username,
        balance: {
          sum: 0,
          locked: 0,
        },
      };

      await newUserRef.set(userRecord);
      console.log(
        `New Telegram user created: ${userId} (TG ID: ${telegramId})`
      );
    }

    // Create Firebase Auth user and custom token
    const authResult = await createFirebaseAuthUser(
      userId,
      userRecord,
      telegramUser,
      telegramId
    );

    return {
      customToken: authResult.customToken,
      user: userRecord,
    };
  } catch (error) {
    console.error("Telegram authentication error:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError("internal", "Authentication failed");
  }
});
