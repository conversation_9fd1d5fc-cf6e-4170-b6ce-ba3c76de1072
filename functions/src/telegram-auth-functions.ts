import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import {
  getTelegramBotToken,
  isDevelopment,
  CORS_CONFIG,
  getConfig,
} from "./config";
import { UserEntity } from "./types";
import * as CryptoJS from "crypto-js";
import { log } from "./utils/logger";

export interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  photo_url?: string;
}

export interface ValidationResult {
  isValid: boolean;
  user?: TelegramUser;
  error?: string;
}

export function validateTelegramWebAppData(initData: string, botToken: string) {
  try {
    // Parse the init data
    const urlParams = new URLSearchParams(initData);
    const hash = urlParams.get("hash");

    if (!hash) {
      return { isValid: false, error: "Hash is missing" };
    }

    // Remove hash from params for validation
    urlParams.delete("hash");

    // Sort parameters alphabetically and create data check string
    const dataCheckArray: string[] = [];
    for (const [key, value] of Array.from(urlParams.entries()).sort()) {
      dataCheckArray.push(`${key}=${value}`);
    }
    const dataCheckString = dataCheckArray.join("\n");

    // Create secret key using bot token
    const secretKey = CryptoJS.HmacSHA256(botToken, "WebAppData");

    // Calculate expected hash
    const expectedHash = CryptoJS.HmacSHA256(
      dataCheckString,
      secretKey
    ).toString();

    // Verify hash
    if (hash !== expectedHash && !isDevelopment()) {
      return { isValid: false, error: "Invalid hash" };
    }

    // Check auth_date (should not be older than 24 hours)
    const authDate = urlParams.get("auth_date");
    if (!authDate) {
      return { isValid: false, error: "Auth date is missing" };
    }

    const authTimestamp = parseInt(authDate, 10);
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const maxAge = 24 * 60 * 60; // 24 hours in seconds

    if (currentTimestamp - authTimestamp > maxAge) {
      return { isValid: false, error: "Auth data is too old" };
    }

    // Parse user data
    const userParam = urlParams.get("user");
    if (!userParam) {
      return { isValid: false, error: "User data is missing" };
    }

    const user: TelegramUser = JSON.parse(userParam);

    return { isValid: true, user };
  } catch (error) {
    return {
      isValid: false,
      error: `Validation error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

async function validateTelegramData(
  initData: string,
  botToken: string,
  isDevMode: boolean
) {
  let telegramUser: TelegramUser;
  let telegramId: string;

  if (isDevMode && initData.includes("mock_hash_for_development")) {
    log.info("Using development mode with mock data", {
      operation: "telegram_auth",
      mode: "development",
    });

    const urlParams = new URLSearchParams(initData);
    const userParam = urlParams.get("user");

    if (!userParam) {
      throw new HttpsError(
        "invalid-argument",
        "User data is missing in mock initData"
      );
    }

    telegramUser = JSON.parse(userParam);
    telegramId = telegramUser.id.toString();

    log.info("Mock user data parsed", {
      operation: "telegram_auth",
      telegramId,
      username: telegramUser.username,
      firstName: telegramUser.first_name,
    });
  } else {
    log.info("Validating Telegram data", {
      operation: "telegram_auth",
      mode: "production",
    });
    const validation = validateTelegramWebAppData(initData, botToken);

    log.info("Telegram data validation result", {
      operation: "telegram_auth",
      isValid: validation.isValid,
      error: validation.error,
      hasUser: !!validation.user,
    });

    if (!validation.isValid || !validation.user) {
      throw new HttpsError(
        "permission-denied",
        validation.error ?? "Invalid Telegram data"
      );
    }

    telegramUser = validation.user;
    telegramId = telegramUser.id.toString();
  }

  return { telegramUser, telegramId };
}

async function createFirebaseAuthUser(
  userId: string,
  userRecord: UserEntity,
  telegramUser: TelegramUser,
  telegramId: string
): Promise<{ customToken: string }> {
  try {
    // First, try to get existing Firebase Auth user
    try {
      await admin.auth().getUser(userId);
      log.info("Found existing Firebase Auth user", {
        operation: "firebase_auth",
        userId,
      });
    } catch (getUserError: any) {
      if (getUserError?.errorInfo?.code === "auth/user-not-found") {
        // Create Firebase Auth user if it doesn't exist
        log.info("Creating new Firebase Auth user", {
          operation: "firebase_auth",
          userId,
        });
        await admin.auth().createUser({
          uid: userId,
          displayName: userRecord.displayName ?? undefined,
          photoURL: userRecord.photoURL ?? undefined,
        });
        log.info("Firebase Auth user created successfully", {
          operation: "firebase_auth",
          userId,
        });
      } else {
        throw getUserError;
      }
    }

    // Prepare custom claims for the token
    const customClaims = {
      tg_id: telegramId,
      provider: "telegram",
      telegram_user: {
        id: telegramUser.id,
        first_name: telegramUser.first_name,
        last_name: telegramUser.last_name,
        username: telegramUser.username,
      },
    };

    // Set custom claims on the Firebase Auth user
    await admin.auth().setCustomUserClaims(userId, customClaims);
    log.info("Custom claims set successfully", {
      operation: "firebase_auth",
      userId,
    });

    log.info("App config loaded", {
      operation: "firebase_auth",
      config: getConfig(),
    });

    // Create custom token using Firebase Admin SDK
    const customToken = await admin
      .auth()
      .createCustomToken(userId, customClaims);
    log.info("Custom token created successfully", {
      operation: "firebase_auth",
      userId,
    });

    return { customToken };
  } catch (authError: any) {
    log.error("Firebase Auth user creation error", authError, {
      operation: "firebase_auth",
      userId,
    });

    // Handle specific IAM permission errors
    if (
      authError?.message?.includes("iam.serviceAccounts.signBlob") ||
      authError?.errorInfo?.code === "auth/insufficient-permission"
    ) {
      throw new HttpsError(
        "failed-precondition",
        "Firebase service account lacks required IAM permissions for custom token creation. Please grant 'Service Account Token Creator' role."
      );
    }

    // Re-throw other auth errors
    throw new HttpsError(
      "internal",
      `Firebase Auth error: ${authError.message || "Unknown error"}`
    );
  }
}

export const signInWithTelegram = onCall<{
  initData: string;
  useLocalBotToken?: boolean;
}>({ cors: CORS_CONFIG }, async (request) => {
  log.info("Starting Telegram authentication", {
    operation: "telegram_signin",
  });

  try {
    const isDevMode = isDevelopment();
    const { initData, useLocalBotToken } = request.data;

    if (!initData) {
      throw new HttpsError("invalid-argument", "initData is required");
    }

    log.info("Telegram authentication mode", {
      operation: "telegram_signin",
      isDevMode,
    });

    // Get bot token from Firebase config
    const botToken = getTelegramBotToken(useLocalBotToken);
    if (!botToken) {
      throw new HttpsError(
        "failed-precondition",
        "Telegram bot token not configured"
      );
    }

    const db = admin.firestore();

    // Validate and extract Telegram user data
    const { telegramUser, telegramId } = await validateTelegramData(
      initData,
      botToken,
      isDevMode
    );

    log.info("Final user data validated", {
      operation: "telegram_signin",
      telegramId,
      firstName: telegramUser.first_name,
      username: telegramUser.username,
    });

    // Check if user already exists
    const existingUserQuery = await db
      .collection("users")
      .where("tg_id", "==", telegramId)
      .limit(1)
      .get();

    let userId: string;
    let userRecord: UserEntity;

    if (!existingUserQuery.empty) {
      // User exists, get their data
      const existingUserDoc = existingUserQuery.docs[0];
      userId = existingUserDoc.id;
      userRecord = existingUserDoc.data() as UserEntity;
    } else {
      // Create new user
      const newUserRef = db.collection("users").doc();
      userId = newUserRef.id;

      userRecord = {
        id: userId,
        email: null,
        displayName:
          telegramUser.first_name +
          (telegramUser.last_name ? ` ${telegramUser.last_name}` : ""),
        photoURL: telegramUser.photo_url ?? null,
        role: "user",
        tg_id: telegramId,
        telegram_handle: telegramUser.username,
        balance: {
          sum: 0,
          locked: 0,
        },
      };

      await newUserRef.set(userRecord);
      log.info(`New Telegram user created: ${userId} (TG ID: ${telegramId})`, {
        operation: "telegram_signin",
        userId,
        telegramId,
        action: "user_created",
      });
    }

    // Create Firebase Auth user and custom token
    const authResult = await createFirebaseAuthUser(
      userId,
      userRecord,
      telegramUser,
      telegramId
    );

    return {
      customToken: authResult.customToken,
      user: userRecord,
    };
  } catch (error) {
    console.error("Telegram authentication error:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError("internal", "Authentication failed");
  }
});
